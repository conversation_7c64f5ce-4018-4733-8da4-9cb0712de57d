diff --git a/index.js b/index.js
index 282ed310d1bb5e704b9a8cb637a9680c756955d5..a409fce8fcffa5f94daae8c0cf1455a7f48ce3d6 100644
--- a/index.js
+++ b/index.js
@@ -27,7 +27,7 @@ var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;
 var reIsHostCtor = /^\[object .+?Constructor\]$/;
 
 /** Detect free variable `global` from Node.js. */
-var freeGlobal = typeof global == 'object' && global && global.Object === Object && global;
+var freeGlobal = typeof globalThis == 'object' && globalThis && globalThis.Object === Object && globalThis;
 
 /** Detect free variable `self`. */
 var freeSelf = typeof self == 'object' && self && self.Object === Object && self;
