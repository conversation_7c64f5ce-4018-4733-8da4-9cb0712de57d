name: Check Attributions

on:
  push:
    branches:
      - Version-v*
      - release/*

jobs:
  check-attributions:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Check attributions changes
        run: yarn attributions:check
