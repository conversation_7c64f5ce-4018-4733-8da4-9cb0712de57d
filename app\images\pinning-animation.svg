<svg width="41" height="35" viewBox="0 0 41 35" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="41" height="11" rx="2.43903" fill="#D6D9DC">
                                    <animate attributeName="y" values="0;0;11.9199;11.9199;11.9199" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/>
                                    <animate attributeName="opacity" values="1;1;1;1;0.01" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/></rect>
<rect y="12" width="41" height="10.8933" rx="2.43903" fill="#D6D9DC">
                                    <animate attributeName="fill" values="#D6D9DC;#0376C9;#0376C9;#D6D9DC;#D6D9DC" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/>
                                    <animate attributeName="y" values="12;12;0.0534668;0.0534668;0.0534668" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/></rect>
<rect y="23.8933" width="41" height="11" rx="2.43903" fill="#D6D9DC">
                                    <animate attributeName="y" values="23.8933;23.8933;23.8933;23.8933;12" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/></rect>
<g filter="url(#filter0_d_684_20337)">
<ellipse cx="31" cy="18" rx="7.75" ry="7.75" fill="white" fill-opacity="0.9" stroke="#F2F4F6" stroke-width="0.5">
                                    <animate attributeName="rx" values="7.75;5.75;7.75;5.75;7.75" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/>
                                    <animate attributeName="ry" values="7.75;5.75;7.75;5.75;7.75" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/></ellipse>
</g>
<defs>
<filter id="filter0_d_684_20337" x="7" y="-4" width="48" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_684_20337">
                                    </feBlend>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_684_20337" result="shape">
                                    </feBlend>

                                    
                                    <animate attributeName="x" values="7;9;7;9;7" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/>
                                    <animate attributeName="y" values="-4;-2;-4;-2;-4" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/>
                                    <animate attributeName="width" values="48;44;48;44;48" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/>
                                    <animate attributeName="height" values="48;44;48;44;48" begin="0s" dur="3.5s" repeatCount="indefinite" calcMode="linear" keyTimes="0;0.146;0.5;0.854;1"/></filter>
</defs>
</svg>