name: Test lint

on:
  workflow_call:

jobs:
  test-lint:
    name: Test lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Lint
        run: yarn lint

      - name: Verify locales
        run: yarn verify-locales --quiet
