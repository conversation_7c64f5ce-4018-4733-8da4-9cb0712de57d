name: Validate lavamoat policy build

on:
  workflow_call:

jobs:
  validate-lavamoat-policy-build:
    name: Validate lavamoat policy build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Validate lavamoat build policy
        run: yarn lavamoat:build:auto
        env:
          INFURA_PROJECT_ID: 00000000000
          GOOGLE_PROD_CLIENT_ID: 00000000000
          APPLE_PROD_CLIENT_ID: 00000000000
          GOOGLE_BETA_CLIENT_ID: 00000000000
          APPLE_BETA_CLIENT_ID: 00000000000
          GOOGLE_FLASK_CLIENT_ID: 00000000000
          APPLE_FLASK_CLIENT_ID: 00000000000

      - name: Check working tree
        run: |
          if ! git diff --exit-code; then
              echo "::error::Working tree dirty."
              exit 1
          fi
