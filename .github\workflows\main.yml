name: Main

on:
  push:
    branches:
      - main
      - master
      - stable
      - Version-v*
      - release/*
      - trigger-ci*
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
    branches-ignore:
      - master
  merge_group:
  schedule:
    # Run the full suite "overnight," once every half hour from 1:00am UTC until 2:59am UTC.
    # This helps with "Top 10 failed tests on the metamask-extension repository main branch,"
    # especially the Monday morning list, which is otherwise usually a fake empty.
    - cron: '0,30 1-2 * * *'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ !(contains(github.ref, 'refs/heads/main') || contains(github.ref, 'refs/heads/master') || contains(github.ref, 'refs/heads/stable')) }}

env:
  # For a `pull_request` event, the branch is `github.head_ref``.
  # For a `push` event, the branch is `github.ref_name`.
  BRANCH: ${{ github.head_ref || github.ref_name }}
  # For a `pull_request` event, the fork is `github.event.pull_request.head.repo.fork`.
  # For a `push` event, the fork is `github.event.repository.fork`.
  IS_FORK: ${{ github.event.pull_request.head.repo.fork || github.event.repository.fork }}

permissions:
  contents: write # required for releases
  id-token: write # required for s3 uploads

jobs:
  prep-deps:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          cache-node-modules: true
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      # Need to cache `.metamask` folder for the anvil binary
      - name: Cache .metamask folder
        uses: actions/cache/save@v4
        with:
          path: .metamask
          key: .metamask-${{ hashFiles('yarn.lock') }}

  lint-workflows:
    name: Lint workflows
    uses: metamask/github-tools/.github/workflows/lint-workflows.yml@1299bb1de0c6974ae6d0a32c7e8897fe168239ac

  test-lint:
    needs:
      - prep-deps
    uses: ./.github/workflows/test-lint.yml

  test-circular-deps:
    name: Test circular deps
    needs:
      - prep-deps
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Check circular dependencies
        run: yarn circular-deps:check

  repository-health-checks:
    needs:
      - prep-deps
    uses: ./.github/workflows/repository-health-checks.yml

  test-storybook:
    name: Test storybook
    needs:
      - prep-deps
    uses: ./.github/workflows/test-storybook.yml

  validate-lavamoat-policy-build:
    needs:
      - prep-deps
    uses: ./.github/workflows/validate-lavamoat-policy-build.yml

  validate-lavamoat-policy-webapp:
    needs:
      - prep-deps
    uses: ./.github/workflows/validate-lavamoat-policy-webapp.yml

  build-dist-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-dist-browserify
      build-command: ${{ ((github.head_ref || github.ref_name) == 'master' || (github.head_ref || github.ref_name) == 'stable') && 'yarn build prod' || 'yarn build dist' }}
    secrets: inherit

  build-dist-mv2-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-dist-mv2-browserify
      build-command: ${{ ((github.head_ref || github.ref_name) == 'master' || (github.head_ref || github.ref_name) == 'stable') && 'yarn build prod' || 'yarn build dist' }}
      mozilla-lint: true
      enable-mv3: false
    secrets: inherit

  build-beta-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-beta-browserify
      build-command: yarn build --build-type beta dist
    secrets: inherit

  build-beta-mv2-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-beta-mv2-browserify
      build-command: yarn build --build-type beta dist
      mozilla-lint: false # Disabled as it is failing for some reason
      enable-mv3: false
    secrets: inherit

  build-flask-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-flask-browserify
      build-command: ${{ ((github.head_ref || github.ref_name) == 'master' || (github.head_ref || github.ref_name) == 'stable') && 'yarn build --build-type flask prod' || 'yarn build --build-type flask dist' }}
    secrets: inherit

  build-flask-mv2-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-flask-mv2-browserify
      build-command: ${{ ((github.head_ref || github.ref_name) == 'master' || (github.head_ref || github.ref_name) == 'stable') && 'yarn build --build-type flask prod' || 'yarn build --build-type flask dist' }}
      mozilla-lint: true
      enable-mv3: false
    secrets: inherit

  build-test-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-test-browserify
      build-command: yarn build:test
    secrets: inherit

  build-test-mv2-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-test-mv2-browserify
      build-command: yarn build:test:mv2
      mozilla-lint: false # Disabled as it is failing for some reason
      enable-mv3: false
    secrets: inherit

  build-test-flask-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-test-flask-browserify
      build-command: yarn build:test:flask
    secrets: inherit

  build-test-flask-mv2-browserify:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-test-flask-mv2-browserify
      build-command: yarn build:test:flask:mv2
      mozilla-lint: false # Disabled as it is failing for some reason
      enable-mv3: false
    secrets: inherit

  build-test-webpack:
    uses: ./.github/workflows/run-build.yml
    with:
      build-name: build-test-webpack
      build-command: yarn build:test:webpack
      validate-source-maps: false # Disabled as webpack outputs are not supported by validate-source-maps
    secrets: inherit

  run-benchmarks:
    uses: ./.github/workflows/run-benchmarks.yml
    needs:
      - prep-deps
      - build-test-browserify
      - build-test-webpack

  run-tests:
    name: Run tests
    needs:
      - prep-deps
    uses: ./.github/workflows/run-tests.yml

  bundle-size:
    needs:
      - build-dist-browserify
    runs-on: ubuntu-latest
    env:
      EXTENSION_BUNDLESIZE_STATS_TOKEN: ${{ secrets.EXTENSION_BUNDLESIZE_STATS_TOKEN }}
      SELENIUM_BROWSER: chrome
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Download artifact 'build-dist-browserify'
        uses: actions/download-artifact@v4
        with:
          name: build-dist-browserify

      - name: Measure bundle size
        run: yarn tsx test/e2e/mv3-perf-stats/bundle-size.ts --out test-artifacts/chrome

      - name: Record bundle size at commit
        if: ${{ env.BRANCH == 'main' && env.IS_FORK == 'false'}}
        run: ./.github/scripts/bundle-stats-commit.sh

      - name: Upload 'bundle-size' to S3
        if: ${{ vars.AWS_REGION && vars.AWS_IAM_ROLE && vars.AWS_S3_BUCKET }}
        uses: metamask/github-tools/.github/actions/upload-s3@1233659b3850eb84824d7375e2e0c58eb237701d
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ vars.AWS_IAM_ROLE }}
          s3-bucket: ${{ vars.AWS_S3_BUCKET }}/${{ github.event.repository.name }}/${{ github.run_id }}/bundle-size
          path: test-artifacts/chrome

  needs-e2e:
    needs:
      - prep-deps
    uses: ./.github/workflows/needs-e2e.yml

  e2e-chrome:
    needs:
      - needs-e2e
      - build-test-browserify
      - build-test-webpack
      - build-dist-browserify
      - build-test-flask-browserify
    if: ${{ needs.needs-e2e.outputs.needs-e2e == 'true' }}
    uses: ./.github/workflows/e2e-chrome.yml
    secrets:
      PR_COMMENT_TOKEN: ${{ secrets.PR_COMMENT_TOKEN }}

  e2e-firefox:
    needs:
      - needs-e2e
      - build-test-mv2-browserify
      - build-test-flask-mv2-browserify
    if: ${{ needs.needs-e2e.outputs.needs-e2e == 'true' }}
    uses: ./.github/workflows/e2e-firefox.yml

  build-storybook:
    name: Build storybook
    uses: ./.github/workflows/build-storybook.yml
    secrets:
      STORYBOOK_TOKEN: ${{ secrets.STORYBOOK_TOKEN }}

  build-ts-migration-dashboard:
    name: Build ts migration dashboard
    uses: ./.github/workflows/build-ts-migration-dashboard.yml
    secrets:
      TS_MIGRATION_DASHBOARD_TOKEN: ${{ secrets.TS_MIGRATION_DASHBOARD_TOKEN }}

  build-source-map-explorer:
    needs:
      - prep-deps
      - build-dist-browserify
    runs-on: ubuntu-latest
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Download artifact 'build-dist-browserify'
        uses: actions/download-artifact@v4
        with:
          name: build-dist-browserify

      - run: ./development/source-map-explorer.sh

      - name: Upload 'source-map-explorer' to S3
        if: ${{ vars.AWS_REGION && vars.AWS_IAM_ROLE && vars.AWS_S3_BUCKET }}
        uses: metamask/github-tools/.github/actions/upload-s3@1233659b3850eb84824d7375e2e0c58eb237701d
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ vars.AWS_IAM_ROLE }}
          s3-bucket: ${{ vars.AWS_S3_BUCKET }}/${{ github.event.repository.name }}/${{ github.run_id }}/source-map-explorer
          path: build-artifacts/source-map-explorer

  build-lavamoat-viz:
    needs:
      - prep-deps
      - build-dist-browserify
    runs-on: ubuntu-latest
    env:
      INFURA_PROJECT_ID: ${{ secrets.INFURA_PROJECT_ID }}
      GOOGLE_PROD_CLIENT_ID: 00000000000
      APPLE_PROD_CLIENT_ID: 00000000000
      GOOGLE_BETA_CLIENT_ID: 00000000000
      APPLE_BETA_CLIENT_ID: 00000000000
      GOOGLE_FLASK_CLIENT_ID: 00000000000
      APPLE_FLASK_CLIENT_ID: 00000000000

    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Download artifact 'build-dist-browserify'
        uses: actions/download-artifact@v4
        with:
          name: build-dist-browserify

      - run: ./.github/scripts/create-lavamoat-viz.sh

      - name: Upload 'build-viz' to S3
        if: ${{ vars.AWS_REGION && vars.AWS_IAM_ROLE && vars.AWS_S3_BUCKET }}
        uses: metamask/github-tools/.github/actions/upload-s3@1233659b3850eb84824d7375e2e0c58eb237701d
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ vars.AWS_IAM_ROLE }}
          s3-bucket: ${{ vars.AWS_S3_BUCKET }}/${{ github.event.repository.name }}/${{ github.run_id }}/lavamoat-viz
          path: build-artifacts/build-viz

  publish-prerelease:
    name: Publish prerelease
    needs:
      - build-dist-browserify
      - build-dist-mv2-browserify
      - build-beta-browserify
      - build-beta-mv2-browserify
      - build-flask-browserify
      - build-flask-mv2-browserify
      - build-test-browserify
      - build-test-mv2-browserify
      - build-test-flask-browserify
      - build-test-flask-mv2-browserify
      - run-benchmarks
      - bundle-size
      - build-storybook
      - build-ts-migration-dashboard
      - build-source-map-explorer
      - build-lavamoat-viz
    uses: ./.github/workflows/publish-prerelease.yml
    secrets:
      PR_COMMENT_TOKEN: ${{ secrets.PR_COMMENT_TOKEN }}

  publish-release:
    name: Publish release
    if: ${{ github.event_name == 'push' && (github.ref_name == 'master' || github.ref_name == 'stable') }}
    needs:
      - build-dist-browserify
      - build-dist-mv2-browserify
      - build-flask-browserify
      - build-flask-mv2-browserify
      - run-tests
      - e2e-chrome
      - e2e-firefox
    uses: ./.github/workflows/publish-release.yml
    secrets: inherit

  # Explanation for all-jobs-completed and all-jobs-pass being separate:
  # https://github.com/MetaMask/metamask-module-template/pull/151
  all-jobs-completed:
    name: All jobs completed
    runs-on: ubuntu-latest
    needs:
      - lint-workflows
      - test-lint
      - repository-health-checks
      - test-storybook
      - validate-lavamoat-policy-build
      - validate-lavamoat-policy-webapp
      - build-dist-browserify
      - build-dist-mv2-browserify
      - build-beta-browserify
      - build-beta-mv2-browserify
      - build-flask-browserify
      - build-flask-mv2-browserify
      - build-test-browserify
      - build-test-mv2-browserify
      - build-test-flask-browserify
      - build-test-flask-mv2-browserify
      - build-test-webpack
      - run-tests
      - needs-e2e
      - e2e-chrome
      - e2e-firefox
      - build-storybook
      - build-ts-migration-dashboard
      - build-source-map-explorer
      - build-lavamoat-viz
    outputs:
      PASSED: ${{ steps.set-output.outputs.PASSED }}
    steps:
      - name: Set PASSED output
        id: set-output
        run: echo "PASSED=true" >> "$GITHUB_OUTPUT"

  all-jobs-pass:
    name: All jobs pass
    if: ${{ !cancelled() }}
    runs-on: ubuntu-latest
    needs:
      - all-jobs-completed
    steps:
      - name: Check that all jobs have passed
        run: |
          passed="${{ needs.all-jobs-completed.outputs.PASSED }}"
          if [[ $passed != "true" ]]; then
            exit 1
          fi

  log-merge-group-failure:
    name: Log merge group failure
    # Only run this job if the merge group event fails, skip on forks
    if: ${{ github.event_name == 'merge_group' && failure() && !github.event.repository.fork }}
    needs:
      - all-jobs-pass
    uses: metamask/github-tools/.github/workflows/log-merge-group-failure.yml@6bbad335a01fce1a9ec1eabd9515542c225d46c0
    secrets:
      GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
      GOOGLE_SERVICE_ACCOUNT: ${{ secrets.GOOGLE_SERVICE_ACCOUNT }}
      SPREADSHEET_ID: ${{ secrets.GOOGLE_MERGE_QUEUE_SPREADSHEET_ID }}
      SHEET_NAME: ${{ secrets.GOOGLE_MERGE_QUEUE_SHEET_NAME }}
