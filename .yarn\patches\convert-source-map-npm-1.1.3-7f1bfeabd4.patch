diff --git a/index.js b/index.js
index bfe92d1e2cae77974e1962b1e339471382d7bd1e..bee1ffe59ab15bd604254a366b0a800b35f0baca 100644
--- a/index.js
+++ b/index.js
@@ -9,7 +9,7 @@ var mapFileCommentRx =
   /(?:\/\/[@#][ \t]+sourceMappingURL=([^\s'"]+?)[ \t]*$)|(?:\/\*[@#][ \t]+sourceMappingURL=([^\*]+?)[ \t]*(?:\*\/){1}[ \t]*$)/mg
 
 function decodeBase64(base64) {
-  return new Buffer(base64, 'base64').toString();
+  return Buffer.from(base64, 'base64').toString();
 }
 
 function stripComment(sm) {
@@ -60,7 +60,7 @@ Converter.prototype.toJSON = function (space) {
 
 Converter.prototype.toBase64 = function () {
   var json = this.toJSON();
-  return new Buffer(json).toString('base64');
+  return Buffer.from(json).toString('base64');
 };
 
 Converter.prototype.toComment = function (options) {
