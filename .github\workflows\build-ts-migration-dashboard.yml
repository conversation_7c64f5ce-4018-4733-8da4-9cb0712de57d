name: Build ts migration dashboard

on:
  workflow_call:
    secrets:
      TS_MIGRATION_DASHBOARD_TOKEN:
        required: true

jobs:
  build-ts-migration-dashboard:
    runs-on: ubuntu-latest
    env:
      # For a `pull_request` event, the branch is `github.head_ref``.
      # For a `push` event, the branch is `github.ref_name`.
      BRANCH: ${{ github.head_ref || github.ref_name }}
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: true
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Build ts migration dashboard
        run: yarn ts-migration:dashboard:build

      - name: Upload 'ts-migration-dashboard' to S3
        if: ${{ vars.AWS_REGION && vars.AWS_IAM_ROLE && vars.AWS_S3_BUCKET }}
        uses: metamask/github-tools/.github/actions/upload-s3@1233659b3850eb84824d7375e2e0c58eb237701d
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ vars.AWS_IAM_ROLE }}
          s3-bucket: ${{ vars.AWS_S3_BUCKET }}/${{ github.event.repository.name }}/${{ github.run_id }}/ts-migration-dashboard
          path: development/ts-migration-dashboard/build/final

      - name: Deploy ts migration dashboard
        if: ${{ env.BRANCH == 'main' && env.TS_MIGRATION_DASHBOARD_TOKEN }}
        env:
          TS_MIGRATION_DASHBOARD_TOKEN: ${{ secrets.TS_MIGRATION_DASHBOARD_TOKEN }}
        run: |
          git remote add ts-migration-dashboard "https://${TS_MIGRATION_DASHBOARD_TOKEN}@github.com/MetaMask/metamask-extension-ts-migration-dashboard.git"
          git config user.name "MetaMask Bot"
          git config user.email <EMAIL>
          yarn ts-migration:dashboard:deploy
