<svg xmlns="http://www.w3.org/2000/svg" width="76" height="76" fill="none">
<defs>
  <pattern id="no-nfts" width="15.2" height="15.2" patternTransform="scale(5)" patternUnits="userSpaceOnUse">
    <style>
      #space-invader rect {
        fill: #b8b8b8;
      }
    </style>
    <!-- the circle -->
    <rect
      width="14.4"
      height="14.4"
      x=".4"
      y=".4"
      rx="7.2"
      fill="#fff"
      stroke="#bbc0c5"
      stroke-width=".8"
      shape-rendering="geometricPrecision"
    />
      <!-- the space invader -->
    <g id="space-invader" transform="translate(3.6 4.6)">
      <!--
        note: the overflowed blocks, e.g., `x="1.9" width="1.1"`, prevent anti-aliasing artifacts
        from showing between the "pixels" when the shape is scaled down (zoomed out).
      -->
      <!-- Row 0 -->
      <rect width="1" height="1"/>
      <rect width="4" height="1.2" x="2"/>
      <rect width="1" height="1" x="7"/>
      <!-- Row 1 -->
      <rect width="6" height="1" x="1" y="1"/>
      <!-- Row 2 -->
      <rect width="1" height="1.4" x="1" y="1.8"/>
      <rect width="2" height="1.4" x="3" y="1.8"/>
      <rect width="1" height="1.4" x="6" y="1.8"/>
      <!-- Row 3 -->
      <rect width="8" height="1.2" y="3"/>
      <!-- Row 4 -->
      <rect width="2" height="1.2" y="3.8"/>
      <rect width="2" height="1.2" x="6" y="3.8"/>
      <!-- Row 5 -->
      <rect width="1" height="1.1" x="1" y="4.9"/>
      <rect width="1.1" height="1" x="1.9" y="5"/>
      <rect width="1.1" height="1" x="5" y="5"/>
      <rect width="1" height="1.1" x="6" y="4.9"/>
    </g>
  </pattern>
</defs>
<rect width="100%" height="100%" fill="url(#no-nfts)"/>
</svg>