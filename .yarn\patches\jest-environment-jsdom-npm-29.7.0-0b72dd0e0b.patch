diff --git a/build/index.js b/build/index.js
index 2e6c16c33c2f588cf8d6a5717ac776bb8908bd0d..5534a739c6bc7cda9e0f8439656a6215dfacc26a 100644
--- a/build/index.js
+++ b/build/index.js
@@ -95,6 +95,11 @@ class JSDOMEnvironment {
 
     // TODO: remove this ASAP, but it currently causes tests to run really slow
     global.Buffer = Buffer;
+    // Needed to support toChecksumAddress from the ethereumjs-util@7.1.5.
+    // It converts a hex string to a buffer and then expects it to be an instanceof Uint8Array.
+    // And although on Node env that buffer is an instance of Uint8Array, on JSDOM it is not.
+    // So we need to override the jsdom Uint8Array with Node Uint8Array.
+    global.Uint8Array = Uint8Array;
 
     // Report uncaught errors.
     this.errorEventListener = event => {
