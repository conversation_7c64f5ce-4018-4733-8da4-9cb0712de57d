diff --git a/src/loadPolicy.js b/src/loadPolicy.js
index b3053356c739a5f351fd4e271b67e31ee00bb4dc..7daebe4104ce5d799f90068516b1d0aaa58546c0 100644
--- a/src/loadPolicy.js
+++ b/src/loadPolicy.js
@@ -101,10 +101,9 @@ async function loadPolicyAndApplyOverrides({
 
   const finalPolicy = mergePolicy(policy, policyOverride)
 
-  // TODO: Only write if merge results in changes.
-  // Would have to make a deep equal check on whole policy, which is a waste of time.
-  // mergePolicy() should be able to do it in one pass.
-  await fs.writeFile(policyPath, jsonStringifySortedPolicy(finalPolicy))
+  // Skip policy write step to prevent intermittent build failures
+  // The extension validates the policy in a separate step, we don't need it
+  // to be written to disk here.
 
   return finalPolicy
 }
diff --git a/src/scuttle.js b/src/scuttle.js
index c096a1fbf0bfe8a8f22290852881598f74fff4b1..b7438881be5e25b48ea18919a4b642a0b14cc317 100644
--- a/src/scuttle.js
+++ b/src/scuttle.js
@@ -77,6 +77,8 @@ function generateScuttleOpts(globalRef, originalOpts = create(null)) {
     exceptions: [],
     scuttlerName: '',
   }
+  // cache regular expressions to work around https://github.com/MetaMask/metamask-extension/issues/21006
+  const regexCache = new Map()
   const opts = assign(
     create(null),
     originalOpts === true ? defaultOpts : originalOpts,
@@ -109,10 +111,15 @@ function generateScuttleOpts(globalRef, originalOpts = create(null)) {
     if (!except.startsWith('/')) {
       return except
     }
+    if (regexCache.has(except)) {
+      return regexCache.get(except)
+    }
     const parts = except.split('/')
     const pattern = parts.slice(1, -1).join('/')
     const flags = parts[parts.length - 1]
-    return new RegExp(pattern, flags)
+    const re = new RegExp(pattern, flags)
+    regexCache.set(except, re)
+    return re
   }
 }
 
