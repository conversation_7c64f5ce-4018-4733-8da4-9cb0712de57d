import { NetworkController } from '@metamask/network-controller';
import { JsonRpcParams, jsonrpc2, Hex } from '@metamask/utils';
import {
  EXPERIENCES_TYPE,
  FIRST_PARTY_CONTRACT_NAMES,
} from '../../../../shared/constants/first-party-contracts';
import { mockNetworkState } from '../../../../test/stub/networks';
import {
  createTxVerificationMiddleware,
  TxParams,
} from './tx-verification-middleware';

const getMockNetworkController = (chainId: `0x${string}` = '0x1') =>
  ({ state: mockNetworkState({ chainId }) }) as NetworkController;

const mockTrustedSigners: Partial<Record<EXPERIENCES_TYPE, Hex>> = {
  [EXPERIENCES_TYPE.METAMASK_BRIDGE]:
    '******************************************',
};

const jsonRpcTemplate = { jsonrpc: jsonrpc2, id: 1 };

const getMiddlewareParams = (method: string, params: JsonRpcParams = []) => {
  const req = { ...jsonRpcTemplate, method, params };
  const res = { ...jsonRpcTemplate, result: null };
  const next = jest.fn();
  const end = jest.fn();
  return { req, res, next, end };
};

const getBridgeTxParams = (txParams: Partial<TxParams> = {}): [TxParams] => {
  return [
    {
      data: '0x1',
      from: '0x1',
      to: '0x1',
      value: '0x1',
      ...txParams,
    },
  ];
};

describe('tx verification middleware', () => {
  it('ignores methods other than eth_sendTransaction', () => {
    const middleware = createTxVerificationMiddleware(
      getMockNetworkController(),
      mockTrustedSigners,
    );
    const { req, res, next, end } = getMiddlewareParams('foo');
    middleware(req, res, next, end);

    expect(next).toHaveBeenCalledTimes(1);
    expect(end).not.toHaveBeenCalled();
  });

  // @ts-expect-error Our test types are broken
  it.each([
    ['null', null],
    ['string', 'foo'],
    ['plain object', {}],
    ['empty array', []],
    ['array with non-object', ['foo']],
    ['non-string "data"', [{ data: 1 }]],
    ['non-string "from"', [{ data: 'data', from: 1 }]],
    ['non-string "to"', [{ data: 'data', from: 'from', to: 1 }]],
    [
      'non-string "value"',
      [{ data: 'data', from: 'from', to: 'to', value: 1 }],
    ],
    [
      'non-string "chainId"',
      [{ data: 'data', from: 'from', to: 'to', value: 'value', chainId: 1 }],
    ],
    [
      'non-"0x"-prefixed "chainId"',
      [{ data: 'data', from: 'from', to: 'to', value: 'value', chainId: '1' }],
    ],
  ])(
    'ignores invalid params: %s',
    (_: string, invalidParams: JsonRpcParams) => {
      const middleware = createTxVerificationMiddleware(
        getMockNetworkController(),
        mockTrustedSigners,
      );

      const { req, res, next, end } = getMiddlewareParams(
        'eth_sendTransaction',
        invalidParams,
      );
      middleware(req, res, next, end);

      expect(next).toHaveBeenCalledTimes(1);
      expect(end).not.toHaveBeenCalled();
    },
  );

  // @ts-expect-error Our test types are broken
  it.each(Object.keys(FIRST_PARTY_CONTRACT_NAMES['MetaMask Bridge']))(
    'ignores transactions that are not addressed to the bridge contract for chain %s',
    (chainId: `0x${string}`) => {
      const middleware = createTxVerificationMiddleware(
        getMockNetworkController(),
        mockTrustedSigners,
      );

      const { req, res, next, end } = getMiddlewareParams(
        'eth_sendTransaction',
        getBridgeTxParams({ chainId, to: '0x1' }),
      );
      middleware(req, res, next, end);

      expect(next).toHaveBeenCalledTimes(1);
      expect(end).not.toHaveBeenCalled();
    },
  );

  // @ts-expect-error Our test types are broken
  it.each(['0x11111', '0x111', '0x222222'])(
    'ignores transactions that do not have a bridge contract deployed for chain %s',
    (chainId: `0x${string}`) => {
      const middleware = createTxVerificationMiddleware(
        getMockNetworkController(),
        mockTrustedSigners,
      );

      const { req, res, next, end } = getMiddlewareParams(
        'eth_sendTransaction',
        getBridgeTxParams({ chainId, to: '0x1' }),
      );
      middleware(req, res, next, end);

      expect(next).toHaveBeenCalledTimes(1);
      expect(end).not.toHaveBeenCalled();
    },
  );

  it('calls next() if reverse address mapping look up is undefined', () => {
    const middleware = createTxVerificationMiddleware(
      getMockNetworkController(),
      mockTrustedSigners,
    );

    const { req, res, next, end } = getMiddlewareParams(
      'eth_sendTransaction',
      getBridgeTxParams({ ...getFixtures().mapUndefined }),
    );
    middleware(req, res, next, end);

    expect(next).toHaveBeenCalledTimes(1);
    expect(end).not.toHaveBeenCalled();
  });

  it('calls next() if chainId for `to` address does not match', () => {
    const middleware = createTxVerificationMiddleware(
      getMockNetworkController(),
      mockTrustedSigners,
    );

    const { req, res, next, end } = getMiddlewareParams(
      'eth_sendTransaction',
      getBridgeTxParams({ ...getFixtures().mapIncorrectChain }),
    );
    middleware(req, res, next, end);

    expect(next).toHaveBeenCalledTimes(1);
    expect(end).not.toHaveBeenCalled();
  });

  it('calls next() if experience type for `to` address is not an experience to verify', () => {
    const middleware = createTxVerificationMiddleware(
      getMockNetworkController(),
      mockTrustedSigners,
    );

    const { req, res, next, end } = getMiddlewareParams(
      'eth_sendTransaction',
      getBridgeTxParams({ ...getFixtures().mapIncorrectExp }),
    );
    middleware(req, res, next, end);

    expect(next).toHaveBeenCalledTimes(1);
    expect(end).not.toHaveBeenCalled();
  });

  it('passes through a valid bridge transaction', () => {
    const middleware = createTxVerificationMiddleware(
      getMockNetworkController(),
      mockTrustedSigners,
    );

    const { req, res, next, end } = getMiddlewareParams(
      'eth_sendTransaction',
      getBridgeTxParams({ ...getFixtures().valid }),
    );
    middleware(req, res, next, end);

    expect(next).toHaveBeenCalledTimes(1);
    expect(end).not.toHaveBeenCalled();
  });

  it('rejects modified bridge transactions', () => {
    const middleware = createTxVerificationMiddleware(
      getMockNetworkController(),
      mockTrustedSigners,
    );

    const { req, res, next, end } = getMiddlewareParams(
      'eth_sendTransaction',
      getBridgeTxParams({ ...getFixtures().invalid }),
    );
    middleware(req, res, next, end);

    expect(next).not.toHaveBeenCalled();
    expect(end).toHaveBeenCalledTimes(1);
  });
});

/**
 * Returns bridge transaction validation fixtures.
 *
 * @returns The fixtures.
 */
function getFixtures() {
  return {
    mapIncorrectExp: {
      data: '0x3ce33bff0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000470de4df82000000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000f736f636b6574416461707465725632000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002800000000000000000000000003a23f943181408eac424116af7b7790c94cb97a50000000000000000000000003a23f943181408eac424116af7b7790c94cb97a5000000000000000000000000000000000000000000000000000000000000a4b10000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000000000000000014000000000000000000000000000000000000000000000000000009f295cd5f000000000000000000000000000e6b738da243e8fa2a0ed5915645789add5de515200000000000000000000000000000000000000000000000000000000000001280000019fd025dec0000000000000000000000000e672b534ccf9876a7554a1dd1685a2a5c2cc8e8c000000000000000000000000b8901acb165ed027e32754e0ffe830802919727f000000000000000000000000710bda329b2a6224e4b44833de30f38e7f81d564000000000000000000000000000000000000000000000000000000000000a4b100000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000004614942c423e000000000000000000000000000000000000000000000000000000886c98b760000000000000000000000000000000000000000000000000000000019012a41ba800000000000000000000000000000000000000000000000000000000000000c40000000000000000000000000000000000000000000000005dedaf7e04c3f5c842c30ed9a4a19baceb915cdd3e865f0dad99ffca277743a20bac00e0f366e7265f1fcad502791ff49e9c5c98e1841a090df23ce5555051da1c',
      from: '0xe672b534ccf9876a7554a1dd1685a2a5c2cc8e8c',
      to: '0xc7bE520a13dC023A1b34C03F4Abdab8A43653F7B',
      value: '0x470de4df820000',
    },
    mapIncorrectChain: {
      data: '0x3ce33bff0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000470de4df82000000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000f736f636b6574416461707465725632000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002800000000000000000000000003a23f943181408eac424116af7b7790c94cb97a50000000000000000000000003a23f943181408eac424116af7b7790c94cb97a5000000000000000000000000000000000000000000000000000000000000a4b10000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000000000000000014000000000000000000000000000000000000000000000000000009f295cd5f000000000000000000000000000e6b738da243e8fa2a0ed5915645789add5de515200000000000000000000000000000000000000000000000000000000000001280000019fd025dec0000000000000000000000000e672b534ccf9876a7554a1dd1685a2a5c2cc8e8c000000000000000000000000b8901acb165ed027e32754e0ffe830802919727f000000000000000000000000710bda329b2a6224e4b44833de30f38e7f81d564000000000000000000000000000000000000000000000000000000000000a4b100000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000004614942c423e000000000000000000000000000000000000000000000000000000886c98b760000000000000000000000000000000000000000000000000000000019012a41ba800000000000000000000000000000000000000000000000000000000000000c40000000000000000000000000000000000000000000000005dedaf7e04c3f5c842c30ed9a4a19baceb915cdd3e865f0dad99ffca277743a20bac00e0f366e7265f1fcad502791ff49e9c5c98e1841a090df23ce5555051da1c',
      from: '0xe672b534ccf9876a7554a1dd1685a2a5c2cc8e8c',
      to: `0xaEc23140408534b378bf5832defc426dF8604B59`,
      value: '0x470de4df820000',
    },
    mapUndefined: {
      data: '0x3ce33bff0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000470de4df82000000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000f736f636b6574416461707465725632000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002800000000000000000000000003a23f943181408eac424116af7b7790c94cb97a50000000000000000000000003a23f943181408eac424116af7b7790c94cb97a5000000000000000000000000000000000000000000000000000000000000a4b10000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000000000000000014000000000000000000000000000000000000000000000000000009f295cd5f000000000000000000000000000e6b738da243e8fa2a0ed5915645789add5de515200000000000000000000000000000000000000000000000000000000000001280000019fd025dec0000000000000000000000000e672b534ccf9876a7554a1dd1685a2a5c2cc8e8c000000000000000000000000b8901acb165ed027e32754e0ffe830802919727f000000000000000000000000710bda329b2a6224e4b44833de30f38e7f81d564000000000000000000000000000000000000000000000000000000000000a4b100000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000004614942c423e000000000000000000000000000000000000000000000000000000886c98b760000000000000000000000000000000000000000000000000000000019012a41ba800000000000000000000000000000000000000000000000000000000000000c40000000000000000000000000000000000000000000000005dedaf7e04c3f5c842c30ed9a4a19baceb915cdd3e865f0dad99ffca277743a20bac00e0f366e7265f1fcad502791ff49e9c5c98e1841a090df23ce5555051da1c',
      from: '0xe672b534ccf9876a7554a1dd1685a2a5c2cc8e8c',
      to: '0x0439e60F02a8900a951603950d8D4527f400C3f9',
      value: '0x470de4df820000',
    },
    valid: {
      data: '0x3ce33bff0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000470de4df82000000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000f736f636b6574416461707465725632000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002800000000000000000000000003a23f943181408eac424116af7b7790c94cb97a50000000000000000000000003a23f943181408eac424116af7b7790c94cb97a5000000000000000000000000000000000000000000000000000000000000a4b10000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000000000000000014000000000000000000000000000000000000000000000000000009f295cd5f000000000000000000000000000e6b738da243e8fa2a0ed5915645789add5de515200000000000000000000000000000000000000000000000000000000000001280000019fd025dec0000000000000000000000000e672b534ccf9876a7554a1dd1685a2a5c2cc8e8c000000000000000000000000b8901acb165ed027e32754e0ffe830802919727f000000000000000000000000710bda329b2a6224e4b44833de30f38e7f81d564000000000000000000000000000000000000000000000000000000000000a4b100000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000004614942c423e000000000000000000000000000000000000000000000000000000886c98b760000000000000000000000000000000000000000000000000000000019012a41ba800000000000000000000000000000000000000000000000000000000000000c40000000000000000000000000000000000000000000000005dedaf7e04c3f5c842c30ed9a4a19baceb915cdd3e865f0dad99ffca277743a20bac00e0f366e7265f1fcad502791ff49e9c5c98e1841a090df23ce5555051da1c',
      from: '0xe672b534ccf9876a7554a1dd1685a2a5c2cc8e8c',
      to: FIRST_PARTY_CONTRACT_NAMES['MetaMask Bridge']['0x1'],
      value: '0x470de4df820000',
    },
    invalid: {
      data: '0x3ce33bff0000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000470de4df82000000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000d6c6966694164617074657256320000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001c0000000000000000000000000e397c4883ec89ed4fc9d258f00c689708b2799c9000000000000000000000000e397c4883ec89ed4fc9d258f00c689708b2799c9000000000000000000000000000000000000000000000000000000000000a4b10000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000466ebb82ac1000000000000000000000000000000000000000000000000000000000000000014000000000000000000000000000000000000000000000000000009f295cd5f000000000000000000000000000c8c0e780960f954c3426a32b6ab453248d632b59000000000000000000000000000000000000000000000000000000000000006c5a39b10a5d458d62482fa1e7e672b534ccf9876a7554a1dd1685a2a5c2cc8e8c0000a4b10002a9de92aa00576661f103ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd00dfeeddeadbeef8932eb23bad9bddb5cf81426f78279a53c6c3b710000000000000000000000000000000000000000',
      from: '0xe672b534ccf9876a7554a1dd1685a2a5c2cc8e8c',
      to: FIRST_PARTY_CONTRACT_NAMES['MetaMask Bridge']['0x1'],
      value: '0x470de4df820000',
    },
  } as const;
}
