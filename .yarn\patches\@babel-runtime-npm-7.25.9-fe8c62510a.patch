diff --git a/helpers/construct.js b/helpers/construct.js
index aee8e70448824f509d6605e2dfa4455167442f21..00a69eba8d4c15a1f9aa318a50abb96c2ec447d9 100644
--- a/helpers/construct.js
+++ b/helpers/construct.js
@@ -1,10 +1,22 @@
 var isNativeReflectConstruct = require("./isNativeReflectConstruct.js");
-var setPrototypeOf = require("./setPrototypeOf.js");
-function _construct(t, e, r) {
-  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);
-  var o = [null];
-  o.push.apply(o, e);
-  var p = new (t.bind.apply(t, o))();
-  return r && setPrototypeOf(p, r.prototype), p;
-}
-module.exports = _construct, module.exports.__esModule = true, module.exports["default"] = module.exports;
\ No newline at end of file
+// All of MetaMask's supported browsers include `Reflect.construct` support, so
+// we don't need this polyfill.
+
+// This Proxy preserves the two properties that were added by `@babel/runtime`.
+// I am not entire sure what these properties are for (maybe ES5/ES6
+// interoperability?) but they have been preserved just in case.
+const reflectProxy = new Proxy(
+  Reflect.construct,
+  {
+    get: function (target, property) {
+      if (property === 'default') {
+        return target;
+      } else if (property === '__esModule') {
+        return true;
+      }
+      return Reflect.get(...arguments);
+    }
+  }
+);
+
+module.exports = reflectProxy;
