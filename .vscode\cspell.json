{"ignorePaths": ["app/images", "package.json"], "ignoreWords": ["BUNDLESIZE", "Brainstem", "C01LUJL3T98", "C05QXJA7NP8", "acitores", "autofetch", "azuretools", "cids", "d<PERSON><PERSON><PERSON>", "eamodio", "esbenp", "initialisation", "koa<PERSON>an", "mockttp", "multibase", "multicodec", "namelookup", "pluggable", "protobufjs", "regadas", "remotedev", "ritave", "sesify", "siginsights", "superstruct", "testrpc", "txinsights", "unzipper", "webextension", "xvfb", "zstd"], "useGitignore": true, "version": "0.2", "words": ["bignumber", "blockaid", "browserify", "browserlistrc", "<PERSON><PERSON><PERSON>", "cimg", "codecov", "codespace", "codespaces", "corepack", "crossorigin", "datetime", "datetimes", "dedupe", "defi", "depcheck", "devcontainer", "devcontainers", "endregion", "ensdomains", "FONTCONFIG", "foundryup", "hardfork", "hexstring", "Interactable", "ipfs", "jazzicon", "keccak", "lavadome", "lavamoat", "lavapack", "Linea", "lockdown", "metamaskbot", "metamaskrc", "metametrics", "Minipass", "mocharc", "MULTICHAIN", "MULTIPROVIDER", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "npmcli", "onboarded", "pageload", "petnames", "pipefail", "quickstart", "recompiles", "retryable", "safener", "shellcheck", "SIWE", "solana", "sourcemaps", "Sourcify", "sprintf", "stablecoins", "testcase", "TESTFILES", "testid", "tsbuildinfo", "tsconfigs", "typecheck", "yargs", "yarnpkg"]}