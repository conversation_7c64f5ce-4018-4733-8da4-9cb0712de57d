diff --git a/dist/MetaMaskKeyring.d.ts b/dist/MetaMaskKeyring.d.ts
index 992e911b068dca6331c1a0ccab7b72ccf324aa02..f4274ec3246ea01a33e29db445810cf6b1f902f5 100644
--- a/dist/MetaMaskKeyring.d.ts
+++ b/dist/MetaMaskKeyring.d.ts
@@ -4,6 +4,7 @@ export declare class MetaMaskKeyring extends BaseKeyring {
     static type: string;
     static instance: MetaMaskKeyring;
     constructor(opts?: StoredKeyring);
+    addAccounts(n?: number): Promise<string[]>;
     getInteraction: () => MetamaskInteractionProvider;
     resetStore: () => void;
     getMemStore: () => import("./MetaMaskInteractionProvider").IMemState;
diff --git a/dist/metamask-airgapped-keyring.cjs.development.js b/dist/metamask-airgapped-keyring.cjs.development.js
index 61aaa831a1a8f064f9429b1e352eb6964ceac934..c25e5f12970b0757a0ae47f81e48e368a718bdd0 100644
--- a/dist/metamask-airgapped-keyring.cjs.development.js
+++ b/dist/metamask-airgapped-keyring.cjs.development.js
@@ -178,6 +178,15 @@ class MetaMaskKeyring extends baseEthKeyring.BaseKeyring {
     }
     MetaMaskKeyring.instance = this;
   }
+  /**
+  * PATCH INFORMATION
+  * The addAccounts method from keyrings is now expected to return only newly created accounts.
+  * This patch overrides the method and its return value to ensure it behaves as intended.
+  */
+  async addAccounts(n = 1) {
+    const accounts = await super.addAccounts(n);
+    return accounts.slice(-1 * n);
+  }
   async signTransaction(address, tx$1) {
     const dataType = tx$1.type === 0 ? bcUrRegistryEth.DataType.transaction : bcUrRegistryEth.DataType.typedTransaction;
     let messageToSign;
diff --git a/dist/metamask-airgapped-keyring.cjs.production.min.js b/dist/metamask-airgapped-keyring.cjs.production.min.js
index 98882f3f14b9134a825e61b5d62fe431a6bdf8d5..86c5680e34107b821f2a354ebf033f3cb1722797 100644
--- a/dist/metamask-airgapped-keyring.cjs.production.min.js
+++ b/dist/metamask-airgapped-keyring.cjs.production.min.js
@@ -1,2 +1,2 @@
-"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@keystonehq/base-eth-keyring"),t=require("events"),s=require("@metamask/obs-store"),n=require("@keystonehq/bc-ur-registry-eth"),i=require("uuid"),r=require("@ethereumjs/tx"),c=require("@ethereumjs/rlp");class o extends t.EventEmitter{constructor(){if(super(),this.cleanSyncListeners=()=>{this.removeAllListeners("keystone-sync_success-hdkey"),this.removeAllListeners("keystone-sync_success-account"),this.removeAllListeners("keystone-sync_cancel")},this.cleanSignListeners=e=>{this.removeAllListeners(e+"-signed"),this.removeAllListeners(e+"-canceled")},this.readCryptoHDKeyOrCryptoAccount=()=>new Promise((e,t)=>{this.memStore.updateState({sync:{reading:!0}}),this.on("keystone-sync_success-hdkey",t=>{const s=n.CryptoHDKey.fromCBOR(Buffer.from(t,"hex"));this.resetState(),e(s)}),this.on("keystone-sync_success-account",t=>{const s=n.CryptoAccount.fromCBOR(Buffer.from(t,"hex"));this.resetState(),e(s)}),this.on("keystone-sync_cancel",()=>{this.resetState(),t(new Error("KeystoneError#sync_cancel. Sync process canceled, please retry"))})}),this.submitCryptoHDKey=e=>{this.emit("keystone-sync_success-hdkey",e)},this.submitCryptoAccount=e=>{this.emit("keystone-sync_success-account",e)},this.cancelSync=()=>{this.emit("keystone-sync_cancel")},this.requestSignature=(e,t,s)=>new Promise((r,c)=>{const o=e.toUR(),a=e.getRequestId(),h=i.stringify(a),u={requestId:h,payload:{type:o.type,cbor:o.cbor.toString("hex")},title:t,description:s};this.memStore.updateState({sign:{request:u}}),this.once(h+"-signed",e=>{const t=n.ETHSignature.fromCBOR(Buffer.from(e,"hex"));this.resetState(),r(t)}),this.once(h+"-canceled",()=>{this.resetState(),c(new Error("KeystoneError#Tx_canceled. Signing canceled, please retry"))})}),this.submitSignature=(e,t)=>{this.emit(e+"-signed",t)},this.cancelRequestSignature=()=>{const e=this.memStore.getState().sign.request;if(e){const{requestId:t}=e;this.memStore.updateState({sign:{}}),this.emit(t+"-canceled")}},this.reset=()=>{this.cleanSyncListeners();const e=this.memStore.getState().sign.request;if(e){const{requestId:t}=e;this.cleanSignListeners(t)}this.resetState()},this.resetState=()=>{this.memStore.updateState({sync:{reading:!1},sign:{}})},o.instance)return o.instance;this.memStore=new s.ObservableStore({sync:{reading:!1},sign:{},_version:1}),o.instance=this}}class a extends e.BaseKeyring{constructor(e){if(super(e),this.getInteraction=()=>new o,this.resetStore=()=>{this.getInteraction().reset()},this.getMemStore=()=>this.getInteraction().memStore,this.removeAccount=e=>{if(!this.accounts.map(e=>e.toLowerCase()).includes(e.toLowerCase()))throw new Error(`Address ${e} not found in this keyring`);this.accounts=this.accounts.filter(t=>t.toLowerCase()!==e.toLowerCase())},this.forgetDevice=()=>{this.page=0,this.perPage=5,this.accounts=[],this.currentAccount=0,this.name="QR Hardware",this.initialized=!1,this.xfp="",this.xpub="",this.hdPath="",this.indexes={},this.hdk=void 0,this.paths={}},this.submitCryptoHDKey=this.getInteraction().submitCryptoHDKey,this.submitCryptoAccount=this.getInteraction().submitCryptoAccount,this.submitSignature=this.getInteraction().submitSignature,this.cancelSync=this.getInteraction().cancelSync,this.cancelSignRequest=this.getInteraction().cancelRequestSignature,a.instance)return a.instance.deserialize(e),a.instance;a.instance=this}async signTransaction(e,t){const s=0===t.type?n.DataType.transaction:n.DataType.typedTransaction;let o;o=0===t.type?Buffer.from(c.RLP.encode(t.getMessageToSign())):Buffer.from(t.getMessageToSign());const a=await this._pathFromAddress(e),h=t.common.chainId(),u=i.v4(),y=n.EthSignRequest.constructETHRequest(o,s,a,this.xfp,u,h,e),{r:m,s:g,v:S}=await this.requestSignature(u,y,"Scan with your Keystone",'After your Keystone has signed the transaction, click on "Scan Keystone" to receive the signature'),d=t.toJSON();return d.v=S,d.s=g,d.r=m,d.type=t.type,r.TransactionFactory.fromTxData(d,{common:t.common})}}a.type=e.BaseKeyring.type,exports.MetaMaskKeyring=a;
+"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@keystonehq/base-eth-keyring"),t=require("events"),s=require("@metamask/obs-store"),n=require("@keystonehq/bc-ur-registry-eth"),i=require("uuid"),r=require("@ethereumjs/tx"),c=require("@ethereumjs/rlp");class o extends t.EventEmitter{constructor(){if(super(),this.cleanSyncListeners=()=>{this.removeAllListeners("keystone-sync_success-hdkey"),this.removeAllListeners("keystone-sync_success-account"),this.removeAllListeners("keystone-sync_cancel")},this.cleanSignListeners=e=>{this.removeAllListeners(e+"-signed"),this.removeAllListeners(e+"-canceled")},this.readCryptoHDKeyOrCryptoAccount=()=>new Promise((e,t)=>{this.memStore.updateState({sync:{reading:!0}}),this.on("keystone-sync_success-hdkey",t=>{const s=n.CryptoHDKey.fromCBOR(Buffer.from(t,"hex"));this.resetState(),e(s)}),this.on("keystone-sync_success-account",t=>{const s=n.CryptoAccount.fromCBOR(Buffer.from(t,"hex"));this.resetState(),e(s)}),this.on("keystone-sync_cancel",()=>{this.resetState(),t(new Error("KeystoneError#sync_cancel. Sync process canceled, please retry"))})}),this.submitCryptoHDKey=e=>{this.emit("keystone-sync_success-hdkey",e)},this.submitCryptoAccount=e=>{this.emit("keystone-sync_success-account",e)},this.cancelSync=()=>{this.emit("keystone-sync_cancel")},this.requestSignature=(e,t,s)=>new Promise((r,c)=>{const o=e.toUR(),a=e.getRequestId(),h=i.stringify(a),u={requestId:h,payload:{type:o.type,cbor:o.cbor.toString("hex")},title:t,description:s};this.memStore.updateState({sign:{request:u}}),this.once(h+"-signed",e=>{const t=n.ETHSignature.fromCBOR(Buffer.from(e,"hex"));this.resetState(),r(t)}),this.once(h+"-canceled",()=>{this.resetState(),c(new Error("KeystoneError#Tx_canceled. Signing canceled, please retry"))})}),this.submitSignature=(e,t)=>{this.emit(e+"-signed",t)},this.cancelRequestSignature=()=>{const e=this.memStore.getState().sign.request;if(e){const{requestId:t}=e;this.memStore.updateState({sign:{}}),this.emit(t+"-canceled")}},this.reset=()=>{this.cleanSyncListeners();const e=this.memStore.getState().sign.request;if(e){const{requestId:t}=e;this.cleanSignListeners(t)}this.resetState()},this.resetState=()=>{this.memStore.updateState({sync:{reading:!1},sign:{}})},o.instance)return o.instance;this.memStore=new s.ObservableStore({sync:{reading:!1},sign:{},_version:1}),o.instance=this}}class a extends e.BaseKeyring{constructor(e){if(super(e),this.getInteraction=()=>new o,this.resetStore=()=>{this.getInteraction().reset()},this.getMemStore=()=>this.getInteraction().memStore,this.removeAccount=e=>{if(!this.accounts.map(e=>e.toLowerCase()).includes(e.toLowerCase()))throw new Error(`Address ${e} not found in this keyring`);this.accounts=this.accounts.filter(t=>t.toLowerCase()!==e.toLowerCase())},this.forgetDevice=()=>{this.page=0,this.perPage=5,this.accounts=[],this.currentAccount=0,this.name="QR Hardware",this.initialized=!1,this.xfp="",this.xpub="",this.hdPath="",this.indexes={},this.hdk=void 0,this.paths={}},this.submitCryptoHDKey=this.getInteraction().submitCryptoHDKey,this.submitCryptoAccount=this.getInteraction().submitCryptoAccount,this.submitSignature=this.getInteraction().submitSignature,this.cancelSync=this.getInteraction().cancelSync,this.cancelSignRequest=this.getInteraction().cancelRequestSignature,a.instance)return a.instance.deserialize(e),a.instance;a.instance=this}async addAccounts(e=1){return(await super.addAccounts(e)).slice(-1*e)}async signTransaction(e,t){const s=0===t.type?n.DataType.transaction:n.DataType.typedTransaction;let o;o=0===t.type?Buffer.from(c.RLP.encode(t.getMessageToSign())):Buffer.from(t.getMessageToSign());const a=await this._pathFromAddress(e),h=t.common.chainId(),u=i.v4(),y=n.EthSignRequest.constructETHRequest(o,s,a,this.xfp,u,h,e),{r:m,s:g,v:S}=await this.requestSignature(u,y,"Scan with your Keystone",'After your Keystone has signed the transaction, click on "Scan Keystone" to receive the signature'),d=t.toJSON();return d.v=S,d.s=g,d.r=m,d.type=t.type,r.TransactionFactory.fromTxData(d,{common:t.common})}}a.type=e.BaseKeyring.type,exports.MetaMaskKeyring=a;
 //# sourceMappingURL=metamask-airgapped-keyring.cjs.production.min.js.map
diff --git a/dist/metamask-airgapped-keyring.esm.js b/dist/metamask-airgapped-keyring.esm.js
index 6e3036bac8320a76876a2822536697072009d0ee..558044356fad8ed3fe20aedfae1444ee73237d63 100644
--- a/dist/metamask-airgapped-keyring.esm.js
+++ b/dist/metamask-airgapped-keyring.esm.js
@@ -174,6 +174,15 @@ class MetaMaskKeyring extends BaseKeyring {
     }
     MetaMaskKeyring.instance = this;
   }
+  /**
+  * PATCH INFORMATION
+  * The addAccounts method from keyrings is now expected to return only newly created accounts.
+  * This patch overrides the method and its return value to ensure it behaves as intended.
+  */
+ async addAccounts(n = 1) {
+   const accounts = await super.addAccounts(n);
+   return accounts.slice(-1 * n);
+ }
   async signTransaction(address, tx) {
     const dataType = tx.type === 0 ? DataType.transaction : DataType.typedTransaction;
     let messageToSign;
