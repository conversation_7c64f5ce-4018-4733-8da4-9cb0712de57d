name: Close release bug report issue when release branch gets merged

on:
  pull_request:
    branches:
      - master
      - stable
    types:
      - closed

jobs:
  close-bug-report:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true && ( startsWith(github.event.pull_request.head.ref, 'Version-v') || startsWith(github.event.pull_request.head.ref, 'release/') )
    steps:
      - name: Checkout and setup environment
        uses: MetaMask/action-checkout-and-setup@v1
        with:
          is-high-risk-environment: false
          skip-allow-scripts: true
          yarn-custom-url: ${{ vars.YARN_URL }}

      - name: Close release bug report issue
        env:
          BUG_REPORT_REPO: MetaMask-planning
          BUG_REPORT_TOKEN: ${{ secrets.BUG_REPORT_TOKEN }}
        run: yarn run close-release-bug-report-issue
