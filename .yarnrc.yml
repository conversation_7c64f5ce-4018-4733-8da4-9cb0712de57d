compressionLevel: mixed

enableGlobalCache: false

enableScripts: false

enableTelemetry: false

logFilters:
  - code: YN0004
    level: discard

nodeLinker: node-modules

npmAuditIgnoreAdvisories:
  - 1088783
  - 1092429
  - 1095136
  - 1089718
  - 1092461
  - 1101059
  - 1102472
  - 1103026
  - 1104001
  - 1103932
  - 1104031
  - 1104032
  - ts-custom-error (deprecation)
  - text-encoding (deprecation)
  - popper.js (deprecation)
  - mini-create-react-context (deprecation)
  - uuid (deprecation)
  - "@npmcli/move-file (deprecation)"
  - core-js (deprecation)
  - "@material-ui/core (deprecation)"
  - "@material-ui/styles (deprecation)"
  - "@material-ui/system (deprecation)"
  - "@ensdomains/ens (deprecation)"
  - "@ensdomains/resolver (deprecation)"
  - testrpc (deprecation)
  - cids (deprecation)
  - multibase (deprecation)
  - multicodec (deprecation)
  - eth-sig-util (deprecation)
  - "@metamask/controller-utils (deprecation)"
  - safe-event-emitter (deprecation)
  - crypto (deprecation)
  - webextension-polyfill-ts (deprecation)
  - ripple-lib (deprecation)
  - ethereum-cryptography (deprecation)
  - react-beautiful-dnd (deprecation)
  - ethereumjs-wallet (deprecation)
  - "@trezor/connect-web (deprecation)"
  - "@solana/web3.js (deprecation)"

plugins:
  - path: .yarn/plugins/@yarnpkg/plugin-allow-scripts.cjs
    spec: "https://raw.githubusercontent.com/LavaMoat/LavaMoat/main/packages/yarn-plugin-allow-scripts/bundles/@yarnpkg/plugin-allow-scripts.js"
  - path: .yarn/plugins/@yarnpkg/plugin-engines.cjs
    spec: "https://raw.githubusercontent.com/devoto13/yarn-plugin-engines/main/bundles/%40yarnpkg/plugin-engines.js"
