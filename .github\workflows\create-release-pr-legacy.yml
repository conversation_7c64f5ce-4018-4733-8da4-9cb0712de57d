name: Create Release Pull Request (Legacy)

on:
  workflow_dispatch:
    inputs:
      base-branch:
        description: 'The base branch, tag, or SHA for git operations and the pull request. Usually `main`'
        required: true
      semver-version:
        description: 'A semantic version, eg: x.x.x'
        required: true
      previous-semver-version:
        description: 'Previous semantic version, eg: x.x.x'
        required: true
jobs:
  create-release-pr:
    name: Create Release Pull Request using Github Tools
    uses: MetaMask/github-tools/.github/workflows/create-release-pr.yml@fc6fe1a3fb591f6afa61f0dbbe7698bd50fab9c7
    with:
      platform: extension
      base-branch: ${{ inputs.base-branch }}
      semver-version: ${{ inputs.semver-version }}
      previous-version-tag: Version-v${{ inputs.previous-semver-version }}
      github-tools-version: fc6fe1a3fb591f6afa61f0dbbe7698bd50fab9c7
    secrets:
      # This token needs write permissions to metamask-extension & read permissions to metamask-planning
      github-token: ${{ secrets.PR_TOKEN }}
      google-application-creds-base64: ${{ secrets.GCP_RLS_SHEET_ACCOUNT_BASE64 }}
    permissions:
      contents: write
      pull-requests: write
