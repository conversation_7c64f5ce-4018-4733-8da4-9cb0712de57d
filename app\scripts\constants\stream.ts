// contexts
export const CONTENT_SCRIPT = 'metamask-contentscript';
export const METAMASK_INPAGE = 'metamask-inpage';
export const PHISHING_WARNING_PAGE = 'metamask-phishing-warning-page';

// stream channels
export const METAMASK_COOKIE_HANDLER = 'metamask-cookie-handler';
export const METAMASK_EIP_1193_PROVIDER = 'metamask-provider';
export const METAMASK_CAIP_MULTICHAIN_PROVIDER = 'metamask-multichain-provider';
export const PHISHING_SAFELIST = 'metamask-phishing-safelist';
export const PHISHING_STREAM = 'phishing';

// For more information about these legacy streams, see here:
// https://github.com/MetaMask/metamask-extension/issues/15491
// TODO:LegacyProvider: Delete
export const LEGACY_CONTENT_SCRIPT = 'contentscript';
export const LEGACY_INPAGE = 'inpage';
export const LEG<PERSON>Y_PROVIDER = 'provider';
export const LEGACY_PUBLIC_CONFIG = 'publicConfig';
