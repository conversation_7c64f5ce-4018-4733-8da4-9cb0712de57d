# Storybook

We're currently using [Storybook](https://storybook.js.org/) as part of our design system. To run Storybook and test some of our UI components, clone the repo and run the following:

```
yarn
yarn storybook
```

You should then see:

> info Storybook started on => http://localhost:6006/

In your browser, navigate to http://localhost:6006/ to see the Storybook application. From here, you'll be able to easily view components and even modify some of their properties.
